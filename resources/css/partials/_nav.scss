.navbar-container {
    max-width: 1440px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 80px; // 80px padding on sides
    position: fixed;
    top: 30px;
    left: 0;
    right: 0;
    z-index: 100;

    // Smooth transitions for show/hide
    transform: translateY(0);
    transition: transform 0.4s ease-in-out;

    // Hidden state (when scrolling down)
    &.nav-hidden {
        transform: translateY(-200%);
    }

    // Visible state (when scrolling up or at top)
    &.nav-visible {
        transform: translateY(0);
    }
}

.navbar {
    position: relative;
    width: 100%; // Full width within container
    max-width: 1250px;
    display: flex;
    align-items: center;
    padding: 16px; // Internal padding for nav content
    border-radius: 10rem;
    color: white;
    transition: all 0.4s ease;

    // Fallback for unsupported browsers
    background: rgba(1, 100, 73, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    // Mobile expanded state
    &.nav-mobile-expanded {
        border-radius: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        height: 100%;
        max-width: none;
        width: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
        transform-origin: center top; // Anchor animation to center top

        // Create header layout when expanded
        .nav-logo,
        .nav-mobile-toggle {
            position: absolute;
            z-index: 10;
        }

        // Logo and toggle in header row when expanded
        .nav-logo {
            top: 16px;
            left: 16px;
        }

        .nav-mobile-toggle {
            top: 16px;
            right: 16px;
        }

        // Mobile content takes full height with proper scrolling
        .nav-mobile-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 4rem 1rem 1rem;
            overflow-y: auto;
        }

        // Items container
        .nav-mobile-items {
            flex: 1;
        }
    }
}

// Enhanced frosted glass effect for supported browsers
@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {
    .navbar {
        // Semi-transparent background with brand color tint (#016449)
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        -webkit-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
        -moz-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
        box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        // Apply backdrop filter directly to nav
        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
        backdrop-filter: blur(16px) saturate(140%) brightness(90%);

        // Enhanced backdrop filter for expanded mobile state
        &.nav-mobile-expanded {
            background: rgba(0, 0, 0, 0.6);
            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);
            backdrop-filter: blur(20px) saturate(150%) brightness(80%);
        }
    }
}
// Nav content positioning with proper flexbox layout


.nav-logo {
    // Logo at flex-start (default)
    flex: 0 0 auto;
    max-width: 40px;
    max-height: 40px;
}

.nav-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.nav-collection {
    // Navigation items in center
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    justify-content: center;
}

.nav-collection button {
    box-shadow: none;
}

.nav-cta {
    // CTA at flex-end
    flex: 0 0 auto;
}

.nav-item {
    position: relative;
}

// Base navigation links and buttons
.nav-link,
.nav-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    color: inherit;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

// Arrow animation for dropdowns (chevron to minus)
.nav-toggle,
.nav-toggle-level2 {
    .nav-arrow {
        transition: all 0.3s ease;
        opacity: 1;
    }

    // Hide chevron and show minus when expanded
    &[aria-expanded="true"] {
        .nav-arrow {
            opacity: 0;
            transform: scale(0.8);
        }

        // Add minus icon after chevron fades out
        &::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 2px;
            background-color: currentColor;
            border-radius: 1px;
            opacity: 1;
            transform: scale(1);
            transition: all 0.3s ease 0.1s;
            right: 0.5rem; // Position for desktop dropdowns
        }
    }

    // Initially hide the minus icon
    &::after {
        content: '';
        position: absolute;
        width: 10px;
        height: 2px;
        background-color: currentColor;
        border-radius: 1px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
        right: 0.5rem; // Position for desktop dropdowns
    }
}

// Dropdown containers
.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;

    // Backdrop filter for supported browsers
    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {
        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
        backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    }

    // Show dropdown when parent is active
    .nav-item:hover &,
    .nav-item.nav-active & {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
}

// Level 2 dropdown positioning
.nav-dropdown-level2 {
    top: 0;
    left: 100%;
    margin-top: 0;
    margin-left: 0.5rem;
}

// Dropdown items
.nav-dropdown-item {
    position: relative;
}

.nav-dropdown-link,
.nav-toggle-level2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: inherit;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: -2px;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.nav-cta a {
    @extend .button-menu;
    padding: 16px 24px; // Keep the larger padding for nav
    border-radius: 20rem;
}

// Mobile Menu Toggle Button
.nav-mobile-toggle {
    display: none;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;
    margin-left: auto; // Push to the right side

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }


}

.hamburger-icon {
    display: flex;
    flex-direction: column;
    gap: 3px;
    width: 18px;
    height: 14px;
    order: 2; // Place icon after text
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background-color: currentColor;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.nav-mobile-toggle-text {
    font-size: 0.9rem;
    font-weight: 500;
    order: 1; // Place text before icon
}

// Hamburger animation when menu is open (hamburger to minus)
.nav-mobile-toggle[aria-expanded="true"] {
    .hamburger-line {
        &:nth-child(1) {
            transform: translateY(5px);
        }
        &:nth-child(2) {
            opacity: 1;
            transform: scale(1);
        }
        &:nth-child(3) {
            transform: translateY(-5px);
        }
    }
}

// Mobile Menu Content (inside expanded nav)
.nav-mobile-content {
    display: none;
    flex-direction: column;
    flex: 1;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease 0.2s; // Delay for smooth entrance

    // Show when nav is expanded
    .nav-mobile-expanded & {
        display: flex;
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-mobile-items {
    flex: 1;
    overflow-y: auto;
}

// Mobile Menu Items
.nav-mobile-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0 1rem;
    opacity: 0;
    transform: translateY(10px);
    animation: none; // Reset animation initially

    // Apply animation when nav is expanded
    .nav-mobile-expanded & {
        animation: fadeInUp 0.4s ease forwards;

        // Stagger animation for each item
        &:nth-child(1) { animation-delay: 0.3s; }
        &:nth-child(2) { animation-delay: 0.4s; }
        &:nth-child(3) { animation-delay: 0.5s; }
        &:nth-child(4) { animation-delay: 0.6s; }
        &:nth-child(5) { animation-delay: 0.7s; }
        &:nth-child(6) { animation-delay: 0.8s; }
        &:nth-child(7) { animation-delay: 0.9s; }
        &:nth-child(8) { animation-delay: 1.0s; }
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Split header for items with submenus
.nav-mobile-item-header,
.nav-mobile-subitem-header {
    display: flex;
    align-items: center;
    width: 100%;
}

.nav-mobile-item-header .nav-mobile-link,
.nav-mobile-subitem-header .nav-mobile-link {
    flex: 1;
    padding: 1rem 0;
    text-decoration: none;
    color: inherit;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

// Toggle buttons for submenus
.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }

    // // Animate the chevron icon
    // .nav-arrow {
    //     transition: transform 0.3s ease;
    // }

    // // Rotate chevron when expanded
    // &[aria-expanded="true"] .nav-arrow {
    //     transform: rotate(180deg);
    // }
}

// Icon swap animation: chevron to minus (active)
.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
    .nav-arrow {
        transition: all 0.3s ease;
        opacity: 1;
    }

    // Hide chevron and show minus when expanded
    &[aria-expanded="true"] {
        .nav-arrow {
            opacity: 0;
            transform: scale(0.8);
        }

        // Add minus icon after chevron fades out
        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background-color: currentColor;
            border-radius: 1px;
            opacity: 1;
            transform: scale(1);
            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition
        }
    }

    // Initially hide the minus icon
    &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background-color: currentColor;
        border-radius: 1px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }
}


// Regular mobile links (no submenus)
.nav-mobile-link {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 1rem 0;
    text-decoration: none;
    color: inherit;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

.nav-mobile-link-level1 {
    font-size: 1.2rem;
    font-weight: 600;
}

.nav-mobile-link-level2,
.nav-mobile-subitem-header .nav-mobile-link {
    padding-left: 1rem;
    font-size: 1rem;
    font-weight: 400;
}

.nav-mobile-link-level3 {
    padding-left: 2rem;
    font-size: 0.9rem;
    font-weight: 400;
}

// Mobile Submenus
.nav-mobile-submenu,
.nav-mobile-subsubmenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.nav-mobile-submenu-active {
        max-height: 500px; // Adjust based on content
    }
}

.nav-mobile-subitem {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

// Mobile CTA at bottom
.nav-mobile-cta {
    margin-top: auto;
    padding: 2rem 1rem 1rem;
    opacity: 0;
    transform: translateY(20px);
    animation: none; // Reset animation initially

    // Apply animation when nav is expanded
    .nav-mobile-expanded & {
        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last
    }
}

.nav-mobile-cta-button {
    @extend .button-menu;
    @extend .button-full;
    padding: 16px 24px; // Keep the larger padding for mobile CTA
}

//Decrease padding after 1600px
@media (max-width: 1600px) {
    .navbar-container {
        padding: 0 20px;
    }
}

//Remove CTA after 1200px
@media (max-width: 1200px) {
    .nav-cta {
        display: none;
    }
}

// Mobile navigation breakpoint
@media (max-width: 1024px) {
    .nav-desktop {
        display: none;
    }

    .nav-mobile-toggle {
        display: flex;
    }

    // Ensure proper flex layout in mobile mode
    nav {
        justify-content: space-between;

        .nav-logo {
            flex: 0 0 auto;
        }

        // When expanded, take full viewport with proper margins
        &.nav-mobile-expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            max-width: none;
            width: auto; // Let positioning handle width
            height: auto; // Let positioning handle height
            z-index: 1000;
            box-sizing: border-box; // Include padding and border in size calculation
        }
    }

    // Adjust container for expanded state
    .navbar-container {
        &.nav-expanded {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            padding: 20px;
            z-index: 1000;
            box-sizing: border-box;
        }
    }
}

@media (max-width: 768px) {
    .navbar-container {
        padding: 0 20px; // Reduced padding on mobile
    }

    nav {
        padding: 12px; // Reduced nav padding on mobile
    }
}

// Enhanced hover effects for desktop
@media (min-width: 769px) {
    .nav-item {
        // Hover to show dropdown
        &:hover .nav-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        // Nested hover for level 2
        .nav-dropdown-item:hover .nav-dropdown-level2 {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    }
}

// Animation improvements
.nav-dropdown {
    // Smooth entrance animation
    animation-duration: 0.2s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;

    &.nav-dropdown-level2 {
        animation-delay: 0.1s; // Slight delay for nested dropdowns
    }
}

// Focus management for accessibility
.nav-toggle:focus,
.nav-toggle-level2:focus,
.nav-dropdown-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    background-color: rgba(255, 255, 255, 0.15);
}