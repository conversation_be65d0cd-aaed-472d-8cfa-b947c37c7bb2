{"version": 3, "file": "css/style.css", "mappings": "AACA;EAAI;ACCJ;;ADEA;EACI;EACA;ACCJ;;ADEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ACCJ;;ADEA;EACI;ACCJ;;ADMA;EACI;EACA;EACA;EACA;EACA;ACHJ;;ADMA;EACI;EACA;EACA;EACA;EACA;ACHJ;;ADMA;EACI;EACA;EACA;EACA;EACA;ACHJ;;ADMA;EACI;EACA;EACA;EACA;ACHJ;;ADMA;EACI;EACA;EACA;EACA;ACHJ;;ADMA;EACI;EACA;EACA;EACA;ACHJ;;ADOA;EACI;EACA;EACA;EACA;ACJJ;;ADSI;EACI;ACNR;;ADWA;EACI;IACI;IACA;ECRN;EDWE;IACI;IACA;ECTN;EDYE;IACI;IACA;ECVN;EDaE;IACI;IACA;ECXN;AACF;ADcA;EACI;IACI;IACA;ECZN;EDeE;IACI;IACA;ECbN;EDgBE;IACI;IACA;ECdN;AACF;ADmBA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;ACnBJ;ADsBI;EACI;EACA;ACpBR;ADwBI;EACI;EACA;EACA;ACtBR;ADyBI;EACI;EACA;ACvBR;;AD4BA;EACI;EACA;ACzBJ;AD2BI;EACI;EACA;EACA;ACzBR;;AD8BA;EACI;EACA;AC3BJ;AD6BI;EACI;EACA;EACA;AC3BR;;ADgCA;EACI;EACA;AC7BJ;AD+BI;EACI;EACA;EACA;AC7BR;;ADkCA;EACI;EACA;AC/BJ;;ADkCA;EACI;EACA;AC/BJ;;ADmCA;EACI;EACA;EACA;EACA;AChCJ;ADkCI;EACI;EACA;EACA;EACA;AChCR;ADmCI;EACI;EACA;EACA;ACjCR;;ADqCA;EACI;EACA;EACA;EACA;AClCJ;ADoCI;EACI;EACA;EACA;EACA;AClCR;;ADuCA;EACI;EACA;EACA;ACpCJ;ADsCI;EACI;EACA;ACpCR;;ADyCA;EACI;ACtCJ;;AD0CA;EACI;ACvCJ;;ACxOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;ADyOJ;ACtOI;EACI;ADwOR;ACpOI;EACI;ADsOR;;AClOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;ADmOJ;AChOI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADkOR;AC/NQ;;EAEI;EACA;ADiOZ;AC7NQ;EACI;EACA;AD+NZ;AC5NQ;EACI;EACA;AD8NZ;AC1NQ;EACI;EACA;EACA;EACA;EACA;EACA;AD4NZ;ACxNQ;EACI;AD0NZ;;ACpNA;EACI;IAEI;IACA;IAGA;IACA;IAGA;IACA;EDoNN;ECjNM;IACI;IACA;IACA;EDmNV;AACF;AC7MA;EAEI;EACA;EACA;AD8MJ;;AC3MA;EACI;EACA;EACA;KAAA;AD8MJ;;AC3MA;EAEI;EACA;EACA;EACA;AD6MJ;;AC1MA;EACI;AD6MJ;;AC1MA;EAEI;AD4MJ;;ACzMA;EACI;AD4MJ;;ACxMA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD2MJ;ACzMI;;EACI;AD4MR;ACzMI;;EACI;EACA;AD4MR;;ACrMI;;EACI;EACA;ADyMR;ACpMQ;;EACI;EACA;ADuMZ;ACnMQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADsMZ;ACjMI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADoMR;;AC/LA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADkMJ;AC/LI;EAjBJ;IAkBQ;IACA;EDkMN;AACF;AC/LI;EAEI;EACA;EACA;ADgMR;;AC3LA;EACI;EACA;EACA;EACA;AD8LJ;;AC1LA;EACI;AD6LJ;;AC1LA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD6LJ;AC3LI;;EACI;AD8LR;AC3LI;;EACI;EACA;EACA;AD8LR;;AC1LA;EAEI;EACA;AD4LJ;;ACxLA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD2LJ;ACzLI;EACI;AD2LR;ACxLI;EACI;EACA;AD0LR;;ACpLA;EACI;EACA;EACA;EACA;EACA;EACA;ADuLJ;;ACpLA;EACI;EACA;EACA;EACA;EACA;ADuLJ;;ACpLA;EACI;EACA;EACA;ADuLJ;;ACjLQ;EACI;ADoLZ;AClLQ;EACI;EACA;ADoLZ;AClLQ;EACI;ADoLZ;;AC9KA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;ADiLJ;AC9KI;EACI;EACA;EACA;ADgLR;;AC5KA;EACI;EACA;AD+KJ;;AC3KA;EACI;EACA;EACA;EACA;EACA;AD8KJ;AC3KI;EACI;AD6KR;AC1KQ;EAAiB;AD6KzB;AC5KQ;EAAiB;AD+KzB;AC9KQ;EAAiB;ADiLzB;AChLQ;EAAiB;ADmLzB;AClLQ;EAAiB;ADqLzB;ACpLQ;EAAiB;ADuLzB;ACtLQ;EAAiB;ADyLzB;ACxLQ;EAAiB;AD2LzB;;ACvLA;EACI;IACI;IACA;ED0LN;AACF;ACtLA;;EAEI;EACA;EACA;ADwLJ;;ACrLA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;ADwLJ;ACtLI;;EACI;ADyLR;ACtLI;;EACI;EACA;ADyLR;;ACpLA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADuLJ;ACrLI;;EACI;ADwLR;ACrLI;;EACI;EACA;ADwLR;;ACvKI;;EACI;EACA;AD2KR;ACtKQ;;EACI;EACA;ADyKZ;ACrKQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADwKZ;ACnKI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADsKR;;AChKA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADmKJ;ACjKI;EACI;ADmKR;AChKI;EACI;EACA;ADkKR;;AC9JA;EACI;EACA;ADiKJ;;AC9JA;;EAEI;EACA;EACA;ADiKJ;;AC9JA;EACI;EACA;EACA;ADiKJ;;AC7JA;;EAEI;EACA;EACA;ADgKJ;AC9JI;;EACI;ADiKR;;AC7JA;EACI;ADgKJ;;AC5JA;EACI;EACA;EACA;EACA;EACA;AD+JJ;AC5JI;EACI;AD8JR;;AC1JA;EAGI;AD2JJ;;ACvJA;EACI;IACI;ED0JN;AACF;ACtJA;EACI;IACI;EDwJN;AACF;ACpJA;EACI;IACI;EDsJN;ECnJE;IACI;EDqJN;ECjJE;IACI;EDmJN;ECjJM;IACI;EDmJV;EC/IM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EDiJV;EC3IM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;ED6IV;AACF;ACzIA;EACI;IACI;ED2IN;ECxIE;IACI;ED0IN;AACF;ACtIA;EAGQ;IACI;IACA;IACA;EDsIV;EClIM;IACI;IACA;IACA;EDoIV;AACF;AC/HA;EAEI;EACA;EACA;ADgIJ;AC9HI;EACI;ADgIR;;AC3HA;;;EAGI;EACA;EACA;AD8HJ;;AEr0BA;EACI;EACA;EACA;EACA;EACA;AFw0BJ;;AEr0BA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AFw0BJ;;AEr0BA;EACI;EACA;AFw0BJ;;AEr0BA;EACI;IACI;IACA;EFw0BN;AACF,C", "sources": ["webpack:///./resources/css/partials/_basics.scss", "webpack:///./resources/css/style.scss", "webpack:///./resources/css/partials/_nav.scss", "webpack:///./resources/css/partials/_hero.scss"], "sourcesContent": ["// Basic reset\n* { box-sizing: border-box; }\n\n// Base typography setup\nhtml {\n    font-size: 16px; // Base font size\n    line-height: 1.5;\n}\n\nbody {\n    margin: 0;\n    padding: 0;\n    font-family: 'Inter', sans-serif;\n    font-weight: 400;\n    font-size: 1rem;\n    line-height: 1.6;\n    color: #1a1a1a;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\nmain {\n    overflow-x: hidden;\n}\n\n// Inter Typography Scale\n// Based on a 1.25 (Major Third) scale with optical adjustments for Inter\n\n// Headings\nh1 {\n    font-size: 2.25rem;   // 36px\n    line-height: 1.2;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n    margin: 0 0 1.5rem 0;\n}\n\nh2 {\n    font-size: 1.875rem;  // 30px\n    line-height: 1.25;\n    font-weight: 600;\n    letter-spacing: -0.01em;\n    margin: 0 0 1.25rem 0;\n}\n\nh3 {\n    font-size: 1.5rem;    // 24px\n    line-height: 1.3;\n    font-weight: 600;\n    letter-spacing: -0.005em;\n    margin: 0 0 1rem 0;\n}\n\nh4 {\n    font-size: 1.25rem;   // 20px\n    line-height: 1.35;\n    font-weight: 600;\n    margin: 0 0 1rem 0;\n}\n\nh5 {\n    font-size: 1.125rem;  // 18px\n    line-height: 1.4;\n    font-weight: 600;\n    margin: 0 0 0.75rem 0;\n}\n\nh6 {\n    font-size: 1rem;      // 16px\n    line-height: 1.45;\n    font-weight: 600;\n    margin: 0 0 0.75rem 0;\n}\n\n// Body text\np {\n    font-size: 1rem;      // 16px\n    line-height: 1.6;\n    font-weight: 400;\n    margin: 0 0 1rem 0;\n}\n\n// Remove margin from last child\nh1, h2, h3, h4, h5, h6, p {\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\n// Responsive typography adjustments\n@media (max-width: 768px) {\n    h1 {\n        font-size: 1.875rem;  // 30px on mobile\n        line-height: 1.2;\n    }\n\n    h2 {\n        font-size: 1.5rem;    // 24px on mobile\n        line-height: 1.25;\n    }\n\n    h3 {\n        font-size: 1.25rem;   // 20px on mobile\n        line-height: 1.3;\n    }\n\n    h4 {\n        font-size: 1.125rem;  // 18px on mobile\n        line-height: 1.35;\n    }\n}\n\n@media (max-width: 480px) {\n    h1 {\n        font-size: 1.5rem;    // 24px on small mobile\n        line-height: 1.25;\n    }\n\n    h2 {\n        font-size: 1.25rem;   // 20px on small mobile\n        line-height: 1.3;\n    }\n\n    h3 {\n        font-size: 1.125rem;  // 18px on small mobile\n        line-height: 1.35;\n    }\n}\n\n// Button System\n// Base button styles\nbutton, .button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.75rem 1.5rem;\n    border: none;\n    border-radius: 20rem;\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    transform: translateY(0) scale(1);\n\n    // Remove default button styles\n    background: none;\n    outline: none;\n\n    // Subtle hover animation\n    &:hover {\n        transform: translateY(-1px) scale(1.02);\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n    }\n\n    // Subtle click effect\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Primary button (#9C2B32 background with white text)\n.button-primary, button.primary {\n    background-color: #9C2B32;\n    color: white;\n\n    &:hover {\n        background-color: #cfe7cb;\n        color: #016449;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n    }\n}\n\n// Secondary button (#016449 with white text)\n.button-secondary, button.secondary {\n    background-color: #016449;\n    color: white;\n\n    &:hover {\n        background-color: #cfe7cb;\n        color: #016449;\n        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n    }\n}\n\n// Menu CTA button (black background, primary color on hover)\n.button-menu, button.menu {\n    background-color: #000000;\n    color: white;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);\n    }\n}\n\n// Button sizes\n.button-sm, button.sm {\n    padding: 0.5rem 1rem;\n    font-size: 0.875rem;\n}\n\n.button-lg, button.lg {\n    padding: 1rem 2rem;\n    font-size: 1.125rem;\n}\n\n// Button variants\n.button-outline {\n    background-color: transparent;\n    border: 2px solid #016449;\n    color: #016449;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #016449;\n        color: white;\n        box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n        border-color: #016449;\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n}\n\n.button-outline-primary {\n    background-color: transparent;\n    border: 2px solid #9C2B32;\n    color: #9C2B32;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n        border-color: #9C2B32;\n    }\n}\n\n// Disabled state\nbutton:disabled, .button.disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none !important;\n\n    &:hover {\n        background-color: initial;\n        color: initial;\n    }\n}\n\n// Full width button\n.button-full, button.full {\n    width: 100%;\n}\n\n// Button with icon spacing\n.button-icon, button.icon {\n    gap: 0.5rem;\n}\n\n", "* {\n  box-sizing: border-box;\n}\n\nhtml {\n  font-size: 16px;\n  line-height: 1.5;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 400;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #1a1a1a;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\nmain {\n  overflow-x: hidden;\n}\n\nh1 {\n  font-size: 2.25rem;\n  line-height: 1.2;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n  margin: 0 0 1.5rem 0;\n}\n\nh2 {\n  font-size: 1.875rem;\n  line-height: 1.25;\n  font-weight: 600;\n  letter-spacing: -0.01em;\n  margin: 0 0 1.25rem 0;\n}\n\nh3 {\n  font-size: 1.5rem;\n  line-height: 1.3;\n  font-weight: 600;\n  letter-spacing: -0.005em;\n  margin: 0 0 1rem 0;\n}\n\nh4 {\n  font-size: 1.25rem;\n  line-height: 1.35;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\nh5 {\n  font-size: 1.125rem;\n  line-height: 1.4;\n  font-weight: 600;\n  margin: 0 0 0.75rem 0;\n}\n\nh6 {\n  font-size: 1rem;\n  line-height: 1.45;\n  font-weight: 600;\n  margin: 0 0 0.75rem 0;\n}\n\np {\n  font-size: 1rem;\n  line-height: 1.6;\n  font-weight: 400;\n  margin: 0 0 1rem 0;\n}\n\nh1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child, p:last-child {\n  margin-bottom: 0;\n}\n\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.875rem;\n    line-height: 1.2;\n  }\n  h2 {\n    font-size: 1.5rem;\n    line-height: 1.25;\n  }\n  h3 {\n    font-size: 1.25rem;\n    line-height: 1.3;\n  }\n  h4 {\n    font-size: 1.125rem;\n    line-height: 1.35;\n  }\n}\n@media (max-width: 480px) {\n  h1 {\n    font-size: 1.5rem;\n    line-height: 1.25;\n  }\n  h2 {\n    font-size: 1.25rem;\n    line-height: 1.3;\n  }\n  h3 {\n    font-size: 1.125rem;\n    line-height: 1.35;\n  }\n}\nbutton, .button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 20rem;\n  font-family: \"Inter\", sans-serif;\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  transform: translateY(0) scale(1);\n  background: none;\n  outline: none;\n}\nbutton:hover, .button:hover {\n  transform: translateY(-1px) scale(1.02);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\nbutton:active, .button:active {\n  transform: translateY(0) scale(0.98);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n  transition: all 0.1s ease;\n}\nbutton:focus, .button:focus {\n  outline: 2px solid rgba(1, 100, 73, 0.5);\n  outline-offset: 2px;\n}\n\n.button-primary, button.primary {\n  background-color: #9C2B32;\n  color: white;\n}\n.button-primary:hover, button.primary:hover {\n  background-color: #cfe7cb;\n  color: #016449;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n}\n\n.button-secondary, button.secondary {\n  background-color: #016449;\n  color: white;\n}\n.button-secondary:hover, button.secondary:hover {\n  background-color: #cfe7cb;\n  color: #016449;\n  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n}\n\n.button-menu, .nav-mobile-cta-button, .nav-cta a, button.menu {\n  background-color: #000000;\n  color: white;\n}\n.button-menu:hover, .nav-mobile-cta-button:hover, .nav-cta a:hover, button.menu:hover {\n  background-color: #9C2B32;\n  color: white;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);\n}\n\n.button-sm, button.sm {\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n}\n\n.button-lg, button.lg {\n  padding: 1rem 2rem;\n  font-size: 1.125rem;\n}\n\n.button-outline {\n  background-color: transparent;\n  border: 2px solid #016449;\n  color: #016449;\n  box-shadow: none;\n}\n.button-outline:hover {\n  background-color: #016449;\n  color: white;\n  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);\n  border-color: #016449;\n}\n.button-outline:active {\n  transform: translateY(0) scale(0.98);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n  transition: all 0.1s ease;\n}\n\n.button-outline-primary {\n  background-color: transparent;\n  border: 2px solid #9C2B32;\n  color: #9C2B32;\n  box-shadow: none;\n}\n.button-outline-primary:hover {\n  background-color: #9C2B32;\n  color: white;\n  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);\n  border-color: #9C2B32;\n}\n\nbutton:disabled, .button.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none !important;\n}\nbutton:disabled:hover, .button.disabled:hover {\n  background-color: initial;\n  color: initial;\n}\n\n.button-full, .nav-mobile-cta-button, button.full {\n  width: 100%;\n}\n\n.button-icon, button.icon {\n  gap: 0.5rem;\n}\n\n.navbar-container {\n  max-width: 1440px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  padding: 0 80px;\n  position: fixed;\n  top: 30px;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  transform: translateY(0);\n  transition: transform 0.4s ease-in-out;\n}\n.navbar-container.nav-hidden {\n  transform: translateY(-200%);\n}\n.navbar-container.nav-visible {\n  transform: translateY(0);\n}\n\n.navbar {\n  position: relative;\n  width: 100%;\n  max-width: 1250px;\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-radius: 10rem;\n  color: white;\n  transition: all 0.4s ease;\n  background: rgba(1, 100, 73, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n.navbar.nav-mobile-expanded {\n  border-radius: 1.5rem;\n  flex-direction: column;\n  align-items: stretch;\n  height: 100vh;\n  max-width: none;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  transform-origin: center top;\n}\n.navbar.nav-mobile-expanded .nav-logo,\n.navbar.nav-mobile-expanded .nav-mobile-toggle {\n  position: absolute;\n  z-index: 10;\n}\n.navbar.nav-mobile-expanded .nav-logo {\n  top: 16px;\n  left: 16px;\n}\n.navbar.nav-mobile-expanded .nav-mobile-toggle {\n  top: 16px;\n  right: 16px;\n}\n.navbar.nav-mobile-expanded .nav-mobile-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  padding: 4rem 1rem 1rem;\n  overflow-y: auto;\n}\n.navbar.nav-mobile-expanded .nav-mobile-items {\n  flex: 1;\n}\n\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  .navbar {\n    background: rgba(0, 0, 0, 0.4);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n  .navbar.nav-mobile-expanded {\n    background: rgba(0, 0, 0, 0.6);\n    -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n    backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n  }\n}\n.nav-logo {\n  flex: 0 0 auto;\n  max-width: 40px;\n  max-height: 40px;\n}\n\n.nav-logo img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.nav-collection {\n  display: flex;\n  align-items: center;\n  flex: 1 1 auto;\n  justify-content: center;\n}\n\n.nav-collection button {\n  box-shadow: none;\n}\n\n.nav-cta {\n  flex: 0 0 auto;\n}\n\n.nav-item {\n  position: relative;\n}\n\n.nav-link,\n.nav-toggle {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  color: inherit;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n}\n.nav-link:hover,\n.nav-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-link:focus,\n.nav-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-toggle .nav-arrow,\n.nav-toggle-level2 .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-toggle[aria-expanded=true] .nav-arrow,\n.nav-toggle-level2[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-toggle[aria-expanded=true]::after,\n.nav-toggle-level2[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n  right: 0.5rem;\n}\n.nav-toggle::after,\n.nav-toggle-level2::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n  right: 0.5rem;\n}\n\n.nav-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  min-width: 200px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 0.5rem 0;\n  margin-top: 0.5rem;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px);\n  transition: all 0.2s ease;\n  z-index: 1000;\n}\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  .nav-dropdown {\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n}\n.nav-item:hover .nav-dropdown, .nav-item.nav-active .nav-dropdown {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.nav-dropdown-level2 {\n  top: 0;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.5rem;\n}\n\n.nav-dropdown-item {\n  position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 0.75rem 1rem;\n  text-decoration: none;\n  color: inherit;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s ease;\n}\n.nav-dropdown-link:hover,\n.nav-toggle-level2:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-dropdown-link:focus,\n.nav-toggle-level2:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: -2px;\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-cta a {\n  padding: 16px 24px;\n  border-radius: 20rem;\n}\n\n.nav-mobile-toggle {\n  display: none;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n  margin-left: auto;\n}\n.nav-mobile-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-mobile-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.hamburger-icon {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 18px;\n  height: 14px;\n  order: 2;\n}\n\n.hamburger-line {\n  width: 100%;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n  font-size: 0.9rem;\n  font-weight: 500;\n  order: 1;\n}\n\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(1) {\n  transform: rotate(45deg) translate(3px, 3px);\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(2) {\n  opacity: 0;\n  transform: scale(0);\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(3) {\n  transform: rotate(-45deg) translate(3px, -3px);\n}\n\n.nav-mobile-content {\n  display: none;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  opacity: 0;\n  transform: translateY(20px);\n  transition: all 0.4s ease 0.2s;\n}\n.nav-mobile-expanded .nav-mobile-content {\n  display: flex;\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.nav-mobile-items {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.nav-mobile-item {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 0 1rem;\n  opacity: 0;\n  transform: translateY(10px);\n  animation: none;\n}\n.nav-mobile-expanded .nav-mobile-item {\n  animation: fadeInUp 0.4s ease forwards;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(1) {\n  animation-delay: 0.3s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(2) {\n  animation-delay: 0.4s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(3) {\n  animation-delay: 0.5s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(4) {\n  animation-delay: 0.6s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(5) {\n  animation-delay: 0.7s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(6) {\n  animation-delay: 0.8s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(7) {\n  animation-delay: 0.9s;\n}\n.nav-mobile-expanded .nav-mobile-item:nth-child(8) {\n  animation-delay: 1s;\n}\n\n@keyframes fadeInUp {\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n  flex: 1;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-item-header .nav-mobile-link:hover,\n.nav-mobile-subitem-header .nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-item-header .nav-mobile-link:focus,\n.nav-mobile-subitem-header .nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n}\n.nav-mobile-toggle-item:hover,\n.nav-mobile-toggle-subitem:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-mobile-toggle-item:focus,\n.nav-mobile-toggle-subitem:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item .nav-arrow,\n.nav-mobile-toggle-subitem .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-mobile-toggle-item[aria-expanded=true] .nav-arrow,\n.nav-mobile-toggle-subitem[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-mobile-toggle-item[aria-expanded=true]::after,\n.nav-mobile-toggle-subitem[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n}\n.nav-mobile-toggle-item::after,\n.nav-mobile-toggle-subitem::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-link {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-link-level1 {\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n  padding-left: 1rem;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n  padding-left: 2rem;\n  font-size: 0.9rem;\n  font-weight: 400;\n}\n\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n  max-height: 0;\n  overflow: hidden;\n  transition: max-height 0.3s ease;\n}\n.nav-mobile-submenu.nav-mobile-submenu-active,\n.nav-mobile-subsubmenu.nav-mobile-submenu-active {\n  max-height: 500px;\n}\n\n.nav-mobile-subitem {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.nav-mobile-cta {\n  margin-top: auto;\n  padding: 2rem 1rem 1rem;\n  opacity: 0;\n  transform: translateY(20px);\n  animation: none;\n}\n.nav-mobile-expanded .nav-mobile-cta {\n  animation: fadeInUp 0.4s ease 1.1s forwards;\n}\n\n.nav-mobile-cta-button {\n  padding: 16px 24px;\n}\n\n@media (max-width: 1600px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n}\n@media (max-width: 1200px) {\n  .nav-cta {\n    display: none;\n  }\n}\n@media (max-width: 1024px) {\n  .nav-desktop {\n    display: none;\n  }\n  .nav-mobile-toggle {\n    display: flex;\n  }\n  nav {\n    justify-content: space-between;\n  }\n  nav .nav-logo {\n    flex: 0 0 auto;\n  }\n  nav.nav-mobile-expanded {\n    position: fixed;\n    top: 20px;\n    left: 20px;\n    right: 20px;\n    bottom: 20px;\n    max-width: none;\n    width: auto;\n    height: auto;\n    z-index: 1000;\n  }\n  .navbar-container.nav-expanded {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    padding: 20px;\n    z-index: 1000;\n  }\n}\n@media (max-width: 768px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n  nav {\n    padding: 12px;\n  }\n}\n@media (min-width: 769px) {\n  .nav-item:hover .nav-dropdown {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n  .nav-item .nav-dropdown-item:hover .nav-dropdown-level2 {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n}\n.nav-dropdown {\n  animation-duration: 0.2s;\n  animation-timing-function: ease-out;\n  animation-fill-mode: both;\n}\n.nav-dropdown.nav-dropdown-level2 {\n  animation-delay: 0.1s;\n}\n\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.8);\n  outline-offset: 2px;\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.hero {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 8px;\n}\n\n.hero-content {\n  height: 80vh;\n  min-height: 600px;\n  max-width: 1440px;\n  width: 100%;\n  margin: 8px;\n  background-size: cover !important;\n  background-position: center;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: flex-start;\n  position: relative;\n  overflow: hidden;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);\n  background-blend-mode: normal, normal, color, normal, normal;\n  border-radius: 24px;\n  padding: 2rem;\n}\n\n.banner {\n  width: 50vw;\n  padding: 2rem;\n}\n\n@media (max-width: 768px) {\n  .banner {\n    width: 100%;\n    padding: 0;\n  }\n}", ".navbar-container {\n    max-width: 1440px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    padding: 0 80px; // 80px padding on sides\n    position: fixed;\n    top: 30px;\n    left: 0;\n    right: 0;\n    z-index: 100;\n\n    // Smooth transitions for show/hide\n    transform: translateY(0);\n    transition: transform 0.4s ease-in-out;\n\n    // Hidden state (when scrolling down)\n    &.nav-hidden {\n        transform: translateY(-200%);\n    }\n\n    // Visible state (when scrolling up or at top)\n    &.nav-visible {\n        transform: translateY(0);\n    }\n}\n\n.navbar {\n    position: relative;\n    width: 100%; // Full width within container\n    max-width: 1250px;\n    display: flex;\n    align-items: center;\n    padding: 16px; // Internal padding for nav content\n    border-radius: 10rem;\n    color: white;\n    transition: all 0.4s ease;\n\n    // Fallback for unsupported browsers\n    background: rgba(1, 100, 73, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    // Mobile expanded state\n    &.nav-mobile-expanded {\n        border-radius: 1.5rem;\n        flex-direction: column;\n        align-items: stretch;\n        height: 100vh;\n        max-width: none;\n        width: 100%;\n        margin: 0;\n        padding: 0;\n        overflow: hidden;\n        transform-origin: center top; // Anchor animation to center top\n\n        // Create header layout when expanded\n        .nav-logo,\n        .nav-mobile-toggle {\n            position: absolute;\n            z-index: 10;\n        }\n\n        // Logo and toggle in header row when expanded\n        .nav-logo {\n            top: 16px;\n            left: 16px;\n        }\n\n        .nav-mobile-toggle {\n            top: 16px;\n            right: 16px;\n        }\n\n        // Mobile content takes full height with proper scrolling\n        .nav-mobile-content {\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            height: 100%;\n            padding: 4rem 1rem 1rem;\n            overflow-y: auto;\n        }\n\n        // Items container\n        .nav-mobile-items {\n            flex: 1;\n        }\n    }\n}\n\n// Enhanced frosted glass effect for supported browsers\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n    .navbar {\n        // Semi-transparent background with brand color tint (#016449)\n        background: rgba(0, 0, 0, 0.4);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        -webkit-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        -moz-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n        // Apply backdrop filter directly to nav\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n\n        // Enhanced backdrop filter for expanded mobile state\n        &.nav-mobile-expanded {\n            background: rgba(0, 0, 0, 0.6);\n            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n            backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n        }\n    }\n}\n// Nav content positioning with proper flexbox layout\n\n\n.nav-logo {\n    // Logo at flex-start (default)\n    flex: 0 0 auto;\n    max-width: 40px;\n    max-height: 40px;\n}\n\n.nav-logo img {\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n}\n\n.nav-collection {\n    // Navigation items in center\n    display: flex;\n    align-items: center;\n    flex: 1 1 auto;\n    justify-content: center;\n}\n\n.nav-collection button {\n    box-shadow: none;\n}\n\n.nav-cta {\n    // CTA at flex-end\n    flex: 0 0 auto;\n}\n\n.nav-item {\n    position: relative;\n}\n\n// Base navigation links and buttons\n.nav-link,\n.nav-toggle {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    color: inherit;\n    padding: 0.5rem 1rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Arrow animation for dropdowns (chevron to minus)\n.nav-toggle,\n.nav-toggle-level2 {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 10px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s;\n            right: 0.5rem; // Position for desktop dropdowns\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 10px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n        right: 0.5rem; // Position for desktop dropdowns\n    }\n}\n\n// Dropdown containers\n.nav-dropdown {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    min-width: 200px;\n    background: rgba(0, 0, 0, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 0.5rem;\n    padding: 0.5rem 0;\n    margin-top: 0.5rem;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(-10px);\n    transition: all 0.2s ease;\n    z-index: 1000;\n\n    // Backdrop filter for supported browsers\n    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    }\n\n    // Show dropdown when parent is active\n    .nav-item:hover &,\n    .nav-item.nav-active & {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n    }\n}\n\n// Level 2 dropdown positioning\n.nav-dropdown-level2 {\n    top: 0;\n    left: 100%;\n    margin-top: 0;\n    margin-left: 0.5rem;\n}\n\n// Dropdown items\n.nav-dropdown-item {\n    position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.75rem 1rem;\n    text-decoration: none;\n    color: inherit;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 0.9rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: -2px;\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n}\n\n.nav-cta a {\n    @extend .button-menu;\n    padding: 16px 24px; // Keep the larger padding for nav\n    border-radius: 20rem;\n}\n\n// Mobile Menu Toggle Button\n.nav-mobile-toggle {\n    display: none;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    padding: 0.5rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n    margin-left: auto; // Push to the right side\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n\n\n}\n\n.hamburger-icon {\n    display: flex;\n    flex-direction: column;\n    gap: 3px;\n    width: 18px;\n    height: 14px;\n    order: 2; // Place icon after text\n}\n\n.hamburger-line {\n    width: 100%;\n    height: 2px;\n    background-color: currentColor;\n    border-radius: 1px;\n    transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n    font-size: 0.9rem;\n    font-weight: 500;\n    order: 1; // Place text before icon\n}\n\n// Hamburger animation when menu is open\n.nav-mobile-toggle[aria-expanded=\"true\"] {\n    .hamburger-line {\n        &:nth-child(1) {\n            transform: rotate(45deg) translate(3px, 3px);\n        }\n        &:nth-child(2) {\n            opacity: 0;\n            transform: scale(0);\n        }\n        &:nth-child(3) {\n            transform: rotate(-45deg) translate(3px, -3px);\n        }\n    }\n}\n\n// Mobile Menu Content (inside expanded nav)\n.nav-mobile-content {\n    display: none;\n    flex-direction: column;\n    flex: 1;\n    width: 100%;\n    opacity: 0;\n    transform: translateY(20px);\n    transition: all 0.4s ease 0.2s; // Delay for smooth entrance\n\n    // Show when nav is expanded\n    .nav-mobile-expanded & {\n        display: flex;\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.nav-mobile-items {\n    flex: 1;\n    overflow-y: auto;\n}\n\n// Mobile Menu Items\n.nav-mobile-item {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 0 1rem;\n    opacity: 0;\n    transform: translateY(10px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease forwards;\n\n        // Stagger animation for each item\n        &:nth-child(1) { animation-delay: 0.3s; }\n        &:nth-child(2) { animation-delay: 0.4s; }\n        &:nth-child(3) { animation-delay: 0.5s; }\n        &:nth-child(4) { animation-delay: 0.6s; }\n        &:nth-child(5) { animation-delay: 0.7s; }\n        &:nth-child(6) { animation-delay: 0.8s; }\n        &:nth-child(7) { animation-delay: 0.9s; }\n        &:nth-child(8) { animation-delay: 1.0s; }\n    }\n}\n\n@keyframes fadeInUp {\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n// Split header for items with submenus\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n    display: flex;\n    align-items: center;\n    width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n    flex: 1;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Toggle buttons for submenus\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 48px;\n    height: 48px;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n\n    // // Animate the chevron icon\n    // .nav-arrow {\n    //     transition: transform 0.3s ease;\n    // }\n\n    // // Rotate chevron when expanded\n    // &[aria-expanded=\"true\"] .nav-arrow {\n    //     transform: rotate(180deg);\n    // }\n}\n\n// Icon swap animation: chevron to minus (active)\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 12px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 12px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n    }\n}\n\n\n// Regular mobile links (no submenus)\n.nav-mobile-link {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.nav-mobile-link-level1 {\n    font-size: 1.2rem;\n    font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n    padding-left: 1rem;\n    font-size: 1rem;\n    font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n    padding-left: 2rem;\n    font-size: 0.9rem;\n    font-weight: 400;\n}\n\n// Mobile Submenus\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n    max-height: 0;\n    overflow: hidden;\n    transition: max-height 0.3s ease;\n\n    &.nav-mobile-submenu-active {\n        max-height: 500px; // Adjust based on content\n    }\n}\n\n.nav-mobile-subitem {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n// Mobile CTA at bottom\n.nav-mobile-cta {\n    margin-top: auto;\n    padding: 2rem 1rem 1rem;\n    opacity: 0;\n    transform: translateY(20px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last\n    }\n}\n\n.nav-mobile-cta-button {\n    @extend .button-menu;\n    @extend .button-full;\n    padding: 16px 24px; // Keep the larger padding for mobile CTA\n}\n\n//Decrease padding after 1600px\n@media (max-width: 1600px) {\n    .navbar-container {\n        padding: 0 20px;\n    }\n}\n\n//Remove CTA after 1200px\n@media (max-width: 1200px) {\n    .nav-cta {\n        display: none;\n    }\n}\n\n// Mobile navigation breakpoint\n@media (max-width: 1024px) {\n    .nav-desktop {\n        display: none;\n    }\n\n    .nav-mobile-toggle {\n        display: flex;\n    }\n\n    // Ensure proper flex layout in mobile mode\n    nav {\n        justify-content: space-between;\n\n        .nav-logo {\n            flex: 0 0 auto;\n        }\n\n        // When expanded, take full viewport\n        &.nav-mobile-expanded {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            right: 20px;\n            bottom: 20px;\n            max-width: none;\n            width: auto;\n            height: auto;\n            z-index: 1000;\n        }\n    }\n\n    // Adjust container for expanded state\n    .navbar-container {\n        &.nav-expanded {\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            padding: 20px;\n            z-index: 1000;\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .navbar-container {\n        padding: 0 20px; // Reduced padding on mobile\n    }\n\n    nav {\n        padding: 12px; // Reduced nav padding on mobile\n    }\n}\n\n// Enhanced hover effects for desktop\n@media (min-width: 769px) {\n    .nav-item {\n        // Hover to show dropdown\n        &:hover .nav-dropdown {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n\n        // Nested hover for level 2\n        .nav-dropdown-item:hover .nav-dropdown-level2 {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n    }\n}\n\n// Animation improvements\n.nav-dropdown {\n    // Smooth entrance animation\n    animation-duration: 0.2s;\n    animation-timing-function: ease-out;\n    animation-fill-mode: both;\n\n    &.nav-dropdown-level2 {\n        animation-delay: 0.1s; // Slight delay for nested dropdowns\n    }\n}\n\n// Focus management for accessibility\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.8);\n    outline-offset: 2px;\n    background-color: rgba(255, 255, 255, 0.15);\n}", ".hero {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    padding: 8px;\n}\n\n.hero-content {\n    height: 80vh;\n    min-height: 600px;\n    max-width: 1440px;\n    width: 100%;\n    margin: 8px;\n    background-size: cover !important;\n    background-position: center;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: flex-start;\n    position: relative;\n    overflow: hidden;\n    background-color: rgba(0, 0, 0, 0.5);\n    color: white;\n    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);\n    background-blend-mode: normal, normal, color, normal, normal;\n    border-radius: 24px;\n    padding: 2rem;\n}\n\n.banner {\n    width: 50vw;\n    padding: 2rem;\n}\n\n@media (max-width: 768px) {\n    .banner {\n        width: 100%;\n        padding: 0;\n    }\n}\n\n"], "names": [], "sourceRoot": ""}