* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Inter", sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #1a1a1a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

main {
  overflow-x: hidden;
}

h1 {
  font-size: 2.25rem;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.01em;
  margin: 0 0 1.5rem 0;
}

h2 {
  font-size: 1.875rem;
  line-height: 1.25;
  font-weight: 600;
  letter-spacing: -0.01em;
  margin: 0 0 1.25rem 0;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: -0.005em;
  margin: 0 0 1rem 0;
}

h4 {
  font-size: 1.25rem;
  line-height: 1.35;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

h5 {
  font-size: 1.125rem;
  line-height: 1.4;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

h6 {
  font-size: 1rem;
  line-height: 1.45;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

p {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
  margin: 0 0 1rem 0;
}

h1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child, p:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  h1 {
    font-size: 1.875rem;
    line-height: 1.2;
  }
  h2 {
    font-size: 1.5rem;
    line-height: 1.25;
  }
  h3 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  h4 {
    font-size: 1.125rem;
    line-height: 1.35;
  }
}
@media (max-width: 480px) {
  h1 {
    font-size: 1.5rem;
    line-height: 1.25;
  }
  h2 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  h3 {
    font-size: 1.125rem;
    line-height: 1.35;
  }
}
button, .button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 20rem;
  font-family: "Inter", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(0) scale(1);
  background: none;
  outline: none;
}
button:hover, .button:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
button:active, .button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}
button:focus, .button:focus {
  outline: 2px solid rgba(1, 100, 73, 0.5);
  outline-offset: 2px;
}

.button-primary, button.primary {
  background-color: #9C2B32;
  color: white;
}
.button-primary:hover, button.primary:hover {
  background-color: #cfe7cb;
  color: #016449;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
}

.button-secondary, button.secondary {
  background-color: #016449;
  color: white;
}
.button-secondary:hover, button.secondary:hover {
  background-color: #cfe7cb;
  color: #016449;
  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
}

.button-menu, .nav-mobile-cta-button, .nav-cta a, button.menu {
  background-color: #000000;
  color: white;
}
.button-menu:hover, .nav-mobile-cta-button:hover, .nav-cta a:hover, button.menu:hover {
  background-color: #9C2B32;
  color: white;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);
}

.button-sm, button.sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.button-lg, button.lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.button-outline {
  background-color: transparent;
  border: 2px solid #016449;
  color: #016449;
  box-shadow: none;
}
.button-outline:hover {
  background-color: #016449;
  color: white;
  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
  border-color: #016449;
}
.button-outline:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}

.button-outline-primary {
  background-color: transparent;
  border: 2px solid #9C2B32;
  color: #9C2B32;
  box-shadow: none;
}
.button-outline-primary:hover {
  background-color: #9C2B32;
  color: white;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
  border-color: #9C2B32;
}

button:disabled, .button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}
button:disabled:hover, .button.disabled:hover {
  background-color: initial;
  color: initial;
}

.button-full, .nav-mobile-cta-button, button.full {
  width: 100%;
}

.button-icon, button.icon {
  gap: 0.5rem;
}

.navbar-container {
  max-width: 1440px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0 80px;
  position: fixed;
  top: 30px;
  left: 0;
  right: 0;
  z-index: 100;
  transform: translateY(0);
  transition: transform 0.4s ease-in-out;
}
.navbar-container.nav-hidden {
  transform: translateY(-200%);
}
.navbar-container.nav-visible {
  transform: translateY(0);
}

.navbar {
  position: relative;
  width: 100%;
  max-width: 1250px;
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 10rem;
  color: white;
  transition: all 0.4s ease;
  background: rgba(1, 100, 73, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.navbar.nav-mobile-expanded {
  border-radius: 1.5rem;
  flex-direction: column;
  align-items: stretch;
  height: 100%;
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  transform-origin: center top;
}
.navbar.nav-mobile-expanded .nav-logo,
.navbar.nav-mobile-expanded .nav-mobile-toggle {
  position: absolute;
  z-index: 10;
}
.navbar.nav-mobile-expanded .nav-logo {
  top: 16px;
  left: 16px;
}
.navbar.nav-mobile-expanded .nav-mobile-toggle {
  top: 16px;
  right: 16px;
}
.navbar.nav-mobile-expanded .nav-mobile-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 4rem 1rem 1rem;
  overflow-y: auto;
}
.navbar.nav-mobile-expanded .nav-mobile-items {
  flex: 1;
}

@supports ((-webkit-backdrop-filter: blur(16px)) or (backdrop-filter: blur(16px))) or (-webkit-backdrop-filter: blur(16px)) {
  .navbar {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    backdrop-filter: blur(16px) saturate(140%) brightness(90%);
  }
  .navbar.nav-mobile-expanded {
    background: rgba(0, 0, 0, 0.6);
    -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);
    backdrop-filter: blur(20px) saturate(150%) brightness(80%);
  }
}
.nav-logo {
  flex: 0 0 auto;
  max-width: 40px;
  max-height: 40px;
}

.nav-logo img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}

.nav-collection {
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  justify-content: center;
}

.nav-collection button {
  box-shadow: none;
}

.nav-cta {
  flex: 0 0 auto;
}

.nav-item {
  position: relative;
}

.nav-link,
.nav-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  color: inherit;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}
.nav-link:hover,
.nav-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.nav-link:focus,
.nav-toggle:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-toggle .nav-arrow,
.nav-toggle-level2 .nav-arrow {
  transition: all 0.3s ease;
  opacity: 1;
}
.nav-toggle[aria-expanded=true] .nav-arrow,
.nav-toggle-level2[aria-expanded=true] .nav-arrow {
  opacity: 0;
  transform: scale(0.8);
}
.nav-toggle[aria-expanded=true]::after,
.nav-toggle-level2[aria-expanded=true]::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease 0.1s;
  right: 0.5rem;
}
.nav-toggle::after,
.nav-toggle-level2::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  right: 0.5rem;
}

.nav-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
}
@supports ((-webkit-backdrop-filter: blur(16px)) or (backdrop-filter: blur(16px))) or (-webkit-backdrop-filter: blur(16px)) {
  .nav-dropdown {
    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    backdrop-filter: blur(16px) saturate(140%) brightness(90%);
  }
}
.nav-item:hover .nav-dropdown, .nav-item.nav-active .nav-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-dropdown-level2 {
  top: 0;
  left: 100%;
  margin-top: 0;
  margin-left: 0.5rem;
}

.nav-dropdown-item {
  position: relative;
}

.nav-dropdown-link,
.nav-toggle-level2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: inherit;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}
.nav-dropdown-link:hover,
.nav-toggle-level2:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.nav-dropdown-link:focus,
.nav-toggle-level2:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: -2px;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-cta a {
  padding: 16px 24px;
  border-radius: 20rem;
}

.nav-mobile-toggle {
  display: none;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
  margin-left: auto;
}
.nav-mobile-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.nav-mobile-toggle:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 18px;
  height: 14px;
  order: 2;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.nav-mobile-toggle-text {
  font-size: 0.9rem;
  font-weight: 500;
  order: 1;
}

.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(1) {
  transform: translateY(5px);
}
.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(2) {
  opacity: 1;
  transform: scale(1);
}
.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(3) {
  transform: translateY(-5px);
}

.nav-mobile-content {
  display: none;
  flex-direction: column;
  flex: 1;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease 0.2s;
}
.nav-mobile-expanded .nav-mobile-content {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.nav-mobile-items {
  flex: 1;
  overflow-y: auto;
}

.nav-mobile-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 1rem;
  opacity: 0;
  transform: translateY(10px);
  animation: none;
}
.nav-mobile-expanded .nav-mobile-item {
  animation: fadeInUp 0.4s ease forwards;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(1) {
  animation-delay: 0.3s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(2) {
  animation-delay: 0.4s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(3) {
  animation-delay: 0.5s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(4) {
  animation-delay: 0.6s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(5) {
  animation-delay: 0.7s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(6) {
  animation-delay: 0.8s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(7) {
  animation-delay: 0.9s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(8) {
  animation-delay: 1s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.nav-mobile-item-header,
.nav-mobile-subitem-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-mobile-item-header .nav-mobile-link,
.nav-mobile-subitem-header .nav-mobile-link {
  flex: 1;
  padding: 1rem 0;
  text-decoration: none;
  color: inherit;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.2s ease;
}
.nav-mobile-item-header .nav-mobile-link:hover,
.nav-mobile-subitem-header .nav-mobile-link:hover {
  color: rgba(255, 255, 255, 0.8);
}
.nav-mobile-item-header .nav-mobile-link:focus,
.nav-mobile-subitem-header .nav-mobile-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}
.nav-mobile-toggle-item:hover,
.nav-mobile-toggle-subitem:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.nav-mobile-toggle-item:focus,
.nav-mobile-toggle-subitem:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-toggle-item .nav-arrow,
.nav-mobile-toggle-subitem .nav-arrow {
  transition: all 0.3s ease;
  opacity: 1;
}
.nav-mobile-toggle-item[aria-expanded=true] .nav-arrow,
.nav-mobile-toggle-subitem[aria-expanded=true] .nav-arrow {
  opacity: 0;
  transform: scale(0.8);
}
.nav-mobile-toggle-item[aria-expanded=true]::after,
.nav-mobile-toggle-subitem[aria-expanded=true]::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease 0.1s;
}
.nav-mobile-toggle-item::after,
.nav-mobile-toggle-subitem::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.nav-mobile-link {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
  text-decoration: none;
  color: inherit;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.2s ease;
}
.nav-mobile-link:hover {
  color: rgba(255, 255, 255, 0.8);
}
.nav-mobile-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-link-level1 {
  font-size: 1.2rem;
  font-weight: 600;
}

.nav-mobile-link-level2,
.nav-mobile-subitem-header .nav-mobile-link {
  padding-left: 1rem;
  font-size: 1rem;
  font-weight: 400;
}

.nav-mobile-link-level3 {
  padding-left: 2rem;
  font-size: 0.9rem;
  font-weight: 400;
}

.nav-mobile-submenu,
.nav-mobile-subsubmenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}
.nav-mobile-submenu.nav-mobile-submenu-active,
.nav-mobile-subsubmenu.nav-mobile-submenu-active {
  max-height: 500px;
}

.nav-mobile-subitem {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.nav-mobile-cta {
  margin-top: auto;
  padding: 2rem 1rem 1rem;
  opacity: 0;
  transform: translateY(20px);
  animation: none;
}
.nav-mobile-expanded .nav-mobile-cta {
  animation: fadeInUp 0.4s ease 1.1s forwards;
}

.nav-mobile-cta-button {
  padding: 16px 24px;
}

@media (max-width: 1600px) {
  .navbar-container {
    padding: 0 20px;
  }
}
@media (max-width: 1200px) {
  .nav-cta {
    display: none;
  }
}
@media (max-width: 1024px) {
  .nav-desktop {
    display: none;
  }
  .nav-mobile-toggle {
    display: flex;
  }
  nav {
    justify-content: space-between;
  }
  nav .nav-logo {
    flex: 0 0 auto;
  }
  nav.nav-mobile-expanded {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    max-width: none;
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    z-index: 1000;
    box-sizing: border-box;
  }
  .navbar-container.nav-expanded {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    padding: 20px;
    z-index: 1000;
    box-sizing: border-box;
  }
}
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 20px;
  }
  nav {
    padding: 12px;
  }
}
@media (min-width: 769px) {
  .nav-item:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  .nav-item .nav-dropdown-item:hover .nav-dropdown-level2 {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}
.nav-dropdown {
  animation-duration: 0.2s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}
.nav-dropdown.nav-dropdown-level2 {
  animation-delay: 0.1s;
}

.nav-toggle:focus,
.nav-toggle-level2:focus,
.nav-dropdown-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
  background-color: rgba(255, 255, 255, 0.15);
}

.hero {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.hero-content {
  height: 80vh;
  min-height: 600px;
  max-width: 1440px;
  width: 100%;
  margin: 8px;
  background-size: cover !important;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);
  background-blend-mode: normal, normal, color, normal, normal;
  border-radius: 24px;
  padding: 2rem;
}

.banner {
  width: 50vw;
  padding: 2rem;
}

.hero-cta {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .banner {
    width: 100%;
    padding: 0;
  }
}

/*# sourceMappingURL=style.css.map*/