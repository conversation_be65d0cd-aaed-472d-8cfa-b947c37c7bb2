{"version": 3, "file": "/js/site.js", "mappings": "uBAAIA,E,YCmMG,SAASC,IACd,IAAMC,EAAeC,SAASC,cAAc,sBACtCC,EAAMF,SAASC,cAAc,OAC7BE,EAAkBH,SAASC,cAAc,qBACzCG,EAAgBJ,SAASC,cAAc,uBAExCF,GAAiBG,GAAQC,GAAoBC,IA+ElDL,EAAaM,iBAAiB,SA5E9B,WACoE,SAA/CN,EAAaO,aAAa,iBAG3CC,KAQFL,EAAIM,UAAUC,IAAI,uBAClBN,EAAgBK,UAAUC,IAAI,gBAC9BV,EAAaW,aAAa,gBAAiB,QAC3CX,EAAaW,aAAa,aAAc,cAGxCV,SAASW,KAAKC,MAAMC,SAAW,SAoCjBT,EAAcU,iBAAiB,qCACvCC,SAAQ,SAAAC,GAEZA,EAAKJ,MAAMK,QAAU,IACrBD,EAAKJ,MAAMM,UAAY,mBACvBF,EAAKJ,MAAMO,UAAY,OACvBH,EAAKI,aACLJ,EAAKJ,MAAMO,UAAY,KACvBH,EAAKJ,MAAMK,QAAU,KACrBD,EAAKJ,MAAMM,UAAY,IACzB,IAxDF,IAuEAd,EAAcC,iBAAiB,SAAS,SAACgB,GACvC,IAAMC,EAAeD,EAAME,OAAOC,QAAQ,uDAC1C,GAAIF,EAAc,CAChBD,EAAMI,iBACNJ,EAAMK,kBAEN,IAAMC,EAAaL,EAAaE,QAAQ,yCAClCI,EAAUD,aAAU,EAAVA,EAAY1B,cAAc,+CACpC4B,EAA4D,SAA/CP,EAAahB,aAAa,iBAEzCsB,IACEC,GACFP,EAAaZ,aAAa,gBAAiB,SAC3CkB,EAAQpB,UAAUsB,OAAO,+BAEzBR,EAAaZ,aAAa,gBAAiB,QAC3CkB,EAAQpB,UAAUC,IAAI,8BAG5B,CACF,IAGoBT,SAASc,iBAAiB,4CAClCC,SAAQ,SAAAgB,GAClBA,EAAK1B,iBAAiB,QAAS2B,EACjC,IAGAhC,SAASK,iBAAiB,SAAS,SAACgB,IAC9BnB,EAAIM,UAAUyB,SAAS,wBACtB/B,EAAI+B,SAASZ,EAAME,SACnBxB,EAAakC,SAASZ,EAAME,SAC/BhB,GAEJ,IAGAP,SAASK,iBAAiB,WAAW,SAACgB,GAClB,WAAdA,EAAMa,KAAoBhC,EAAIM,UAAUyB,SAAS,wBACnD1B,GAEJ,IAGA4B,OAAO9B,iBAAiB,UAAU,WAC5B8B,OAAOC,WAAa,MAAQlC,EAAIM,UAAUyB,SAAS,wBACrD1B,GAEJ,KApGA,SAASA,IACPL,EAAIM,UAAUsB,OAAO,uBACrB3B,EAAgBK,UAAUsB,OAAO,gBACjC/B,EAAaW,aAAa,gBAAiB,SAC3CX,EAAaW,aAAa,aAAc,cAGxCV,SAASW,KAAKC,MAAMC,SAAW,GAGXT,EAAcU,iBAAiB,yCACvCC,SAAQ,SAAAC,GAClB,IAAMqB,EAASrB,EAAKf,cAAc,uDAC5B2B,EAAUZ,EAAKf,cAAc,+CAC/BoC,GAAUT,IACZS,EAAO3B,aAAa,gBAAiB,SACrCkB,EAAQpB,UAAUsB,OAAO,6BAE7B,IAGA/B,EAAauC,OACf,CAoBA,SAASN,IAEPzB,GACF,CAwDF,CCrUA,IAAegC,IAST,WACJC,QAAQC,IAAI,yBDZP,WACL,IAAMC,EAAS1C,SAASC,cAAc,qBACtC,GAAKyC,EAAL,CAEA,IAGkBC,EAAMC,EAClBC,EAJFC,EAAcX,OAAOY,QAyCnBC,GAtCYL,EAalB,WACE,IAAMM,EAAiBd,OAAOY,QACLG,KAAKC,IAAIF,EAAiBH,GAG5B,IAEnBG,GAAkB,KAEpBP,EAAOlC,UAAUsB,OAAO,cACxBY,EAAOlC,UAAUC,IAAI,gBACZwC,EAAiBH,GAAeG,EAAiB,KAE1DP,EAAOlC,UAAUC,IAAI,cACrBiC,EAAOlC,UAAUsB,OAAO,gBACfmB,EAAiBH,IAE1BJ,EAAOlC,UAAUsB,OAAO,cACxBY,EAAOlC,UAAUC,IAAI,gBAGvBqC,EAAcG,EAChB,EAnCwBL,EAsC8B,GApC7C,WACL,IAAMQ,EAAOC,UACPC,EAAUC,KACXV,IACHF,EAAKa,MAAMF,EAASF,GACpBP,GAAa,EACbY,YAAW,kBAAMZ,GAAa,CAAK,GAAED,GAEzC,GA+BFT,OAAO9B,iBAAiB,SAAU2C,EAAwB,CAAEU,SAAS,IAGrEhB,EAAOlC,UAAUC,IAAI,cAjDF,CAkDrB,CCrCEkD,GDwCK,WACL,IAAMC,EAAW5D,SAASc,iBAAiB,aAI3C,SAAS+C,EAAkBxC,GACzBA,EAAMI,iBACNJ,EAAMK,kBAEN,IAAMW,EAAShB,EAAMyC,cACfC,EAAU1B,EAAOb,QAAQ,iCACzBK,EAAsD,SAAzCQ,EAAO/B,aAAa,iBAInB,WADAyD,EAAQvC,QAAQ,iBAAmB,SAAW,UAGhExB,SAASc,iBAAiB,aAAaC,SAAQ,SAAAC,GACzCA,IAAS+C,GACXC,EAAchD,EAElB,IAGuB+C,EAAQvC,QAAQ,iBACxBV,iBAAiB,sBAAsBC,SAAQ,SAAAC,GACxDA,IAAS+C,GACXC,EAAchD,EAElB,IAIEa,EACFmC,EAAcD,GAOlB,SAAsBA,GACpB,IAAM1B,EAAS0B,EAAQ9D,cAAc,mCACjCoC,IACFA,EAAO3B,aAAa,gBAAiB,QACrCqD,EAAQvD,UAAUC,IAAI,cAE1B,CAXIwD,CAAaF,EAEjB,CAYA,SAASC,EAAcD,GACrB,IAAM1B,EAAS0B,EAAQ9D,cAAc,mCACjCoC,IACFA,EAAO3B,aAAa,gBAAiB,SACrCqD,EAAQvD,UAAUsB,OAAO,cAGzBiC,EAAQjD,iBAAiB,sBAAsBC,SAAQ,SAAAmD,GACrDF,EAAcE,EAChB,IAEJ,CAGA,SAASC,IACPP,EAAS7C,SAAQ,SAAAgD,GACfC,EAAcD,EAChB,GACF,CAlEKH,EAASQ,SAsGdR,EAAS7C,SAAQ,SAAAgD,GACf,IAAM1B,EAAS0B,EAAQ9D,cAAc,mCACjCoC,GACFA,EAAOhC,iBAAiB,QAASwD,EAErC,IAGA7D,SAASK,iBAAiB,SAAS,SAACgB,GAC7BA,EAAME,OAAOC,QAAQ,cACxB2C,GAEJ,IAGAnE,SAASK,iBAAiB,WAhD1B,SAAuBgB,GACrB,IAAQa,EAAQb,EAARa,IACFmC,EAAgBrE,SAASqE,cAE/B,GAAY,WAARnC,GAWJ,GAAImC,EAAc7C,QAAQ,iBAAkB,CAC1C,IACM8C,EADWD,EAAc7C,QAAQ,iBACJV,iBAAiB,0CAC9CyD,EAAeC,MAAMC,KAAKH,GAAmBI,QAAQL,GAE/C,cAARnC,GACFb,EAAMI,iBAEN6C,GADmBC,EAAe,GAAKD,EAAkBF,QAC5B9B,SACZ,YAARJ,IACTb,EAAMI,iBAEN6C,EADmC,IAAjBC,EAAqBD,EAAkBF,OAAS,EAAIG,EAAe,GACxDjC,QAEjC,OAtBE,GAFA6B,IAEIE,EAAc7C,QAAQ,iBAAkB,CAC1C,IAAMmD,EAAc3E,SAASC,cAAc,eACvC0E,GAAaA,EAAYrC,OAC/B,CAoBJ,IAqBAtC,SAASK,iBAAiB,YAAY,WAEpCoD,YAAW,WACazD,SAASqE,cACZ7C,QAAQ,oBACzB2C,GAEJ,GAAG,IACL,IACF,CCxKES,GAGA9E,GACF,EAnB8B,YAAxBE,SAAS6E,WACX7E,SAASK,iBAAiB,mBAAoBkC,GAE9CA,G,yBCXAuC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CAGAJ,EAAoBO,EAAID,EHzBpBxF,EAAW,GACfkF,EAAoBQ,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIjG,EAASuE,OAAQ0B,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAY9F,EAASiG,GACpCC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAASrB,OAAQ4B,MACpB,EAAXL,GAAsBC,GAAgBD,IAAaM,OAAOC,KAAKnB,EAAoBQ,GAAGY,OAAOjE,GAAS6C,EAAoBQ,EAAErD,GAAKuD,EAASO,MAC9IP,EAASW,OAAOJ,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACblG,EAASuG,OAAON,IAAK,GACrB,IAAIO,EAAIX,SACER,IAANmB,IAAiBb,EAASa,EAC/B,CACD,CACA,OAAOb,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIjG,EAASuE,OAAQ0B,EAAI,GAAKjG,EAASiG,EAAI,GAAG,GAAKH,EAAUG,IAAKjG,EAASiG,GAAKjG,EAASiG,EAAI,GACrGjG,EAASiG,GAAK,CAACL,EAAUC,EAAIC,EAqBjB,EI1BdZ,EAAoBuB,EAAI,CAACC,EAAKC,IAAUP,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAKC,G,MCKlF,IAAII,EAAkB,CACrB,IAAK,EACL,IAAK,EACL,IAAK,GAaN7B,EAAoBQ,EAAES,EAAKa,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4BC,KACvD,IAGIhC,EAAU6B,GAHTpB,EAAUwB,EAAaC,GAAWF,EAGhBlB,EAAI,EAC3B,GAAGL,EAAS0B,MAAMC,GAAgC,IAAxBR,EAAgBQ,KAAa,CACtD,IAAIpC,KAAYiC,EACZlC,EAAoBuB,EAAEW,EAAajC,KACrCD,EAAoBO,EAAEN,GAAYiC,EAAYjC,IAGhD,GAAGkC,EAAS,IAAI1B,EAAS0B,EAAQnC,EAClC,CAEA,IADGgC,GAA4BA,EAA2BC,GACrDlB,EAAIL,EAASrB,OAAQ0B,IACzBe,EAAUpB,EAASK,GAChBf,EAAoBuB,EAAEM,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO9B,EAAoBQ,EAAEC,EAAO,EAGjC6B,EAAqBC,KAAmB,aAAIA,KAAmB,cAAK,GACxED,EAAmBtG,QAAQ+F,EAAqBS,KAAK,KAAM,IAC3DF,EAAmBG,KAAOV,EAAqBS,KAAK,KAAMF,EAAmBG,KAAKD,KAAKF,G,KC/CvFtC,EAAoBQ,OAAEL,EAAW,CAAC,IAAI,MAAM,IAAOH,EAAoB,OACvEA,EAAoBQ,OAAEL,EAAW,CAAC,IAAI,MAAM,IAAOH,EAAoB,OACvE,IAAI0C,EAAsB1C,EAAoBQ,OAAEL,EAAW,CAAC,IAAI,MAAM,IAAOH,EAAoB,OACjG0C,EAAsB1C,EAAoBQ,EAAEkC,E", "sources": ["webpack:///webpack/runtime/chunk loaded", "webpack:///./resources/js/components/nav.js", "webpack:///./resources/js/site.js", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/jsonp chunk loading", "webpack:///webpack/startup"], "sourcesContent": ["var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "/**\n * Navigation Component\n * Handles scroll-based navigation behavior and dropdown interactions for OK Tyr website\n */\n\n// Scroll Navigation Handler\nexport function initScrollNavigation() {\n  const navbar = document.querySelector('.navbar-container');\n  if (!navbar) return;\n\n  let lastScrollY = window.scrollY;\n\n  // Throttle scroll events for better performance\n  function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n      const args = arguments;\n      const context = this;\n      if (!inThrottle) {\n        func.apply(context, args);\n        inThrottle = true;\n        setTimeout(() => inThrottle = false, limit);\n      }\n    }\n  }\n\n  function handleScroll() {\n    const currentScrollY = window.scrollY;\n    const scrollDifference = Math.abs(currentScrollY - lastScrollY);\n\n    // Only react to significant scroll movements (avoid jitter)\n    if (scrollDifference < 5) return;\n\n    if (currentScrollY <= 100) {\n      // Always show nav when near top of page\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    } else if (currentScrollY > lastScrollY && currentScrollY > 200) {\n      // Scrolling down - hide nav\n      navbar.classList.add('nav-hidden');\n      navbar.classList.remove('nav-visible');\n    } else if (currentScrollY < lastScrollY) {\n      // Scrolling up - show nav\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    }\n\n    lastScrollY = currentScrollY;\n  }\n\n  // Use throttled scroll handler\n  const throttledScrollHandler = throttle(handleScroll, 16); // ~60fps\n\n  // Add scroll listener\n  window.addEventListener('scroll', throttledScrollHandler, { passive: true });\n\n  // Initialize nav state\n  navbar.classList.add('nav-visible');\n}\n\n// Dropdown Navigation Handler\nexport function initDropdownNavigation() {\n  const navItems = document.querySelectorAll('.nav-item');\n  if (!navItems.length) return;\n\n  // Handle click events for dropdown toggles\n  function handleToggleClick(event) {\n    event.preventDefault();\n    event.stopPropagation();\n\n    const toggle = event.currentTarget;\n    const navItem = toggle.closest('.nav-item, .nav-dropdown-item');\n    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';\n\n    // Close all other dropdowns at the same level\n    const parentLevel = navItem.closest('.nav-dropdown') ? 'level2' : 'level1';\n    if (parentLevel === 'level1') {\n      // Close all level 1 dropdowns\n      document.querySelectorAll('.nav-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    } else {\n      // Close all level 2 dropdowns in the same parent\n      const parentDropdown = navItem.closest('.nav-dropdown');\n      parentDropdown.querySelectorAll('.nav-dropdown-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    }\n\n    // Toggle current dropdown\n    if (isExpanded) {\n      closeDropdown(navItem);\n    } else {\n      openDropdown(navItem);\n    }\n  }\n\n  // Open dropdown\n  function openDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'true');\n      navItem.classList.add('nav-active');\n    }\n  }\n\n  // Close dropdown\n  function closeDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'false');\n      navItem.classList.remove('nav-active');\n\n      // Also close any child dropdowns\n      navItem.querySelectorAll('.nav-dropdown-item').forEach(childItem => {\n        closeDropdown(childItem);\n      });\n    }\n  }\n\n  // Close all dropdowns\n  function closeAllDropdowns() {\n    navItems.forEach(navItem => {\n      closeDropdown(navItem);\n    });\n  }\n\n  // Handle keyboard navigation\n  function handleKeyDown(event) {\n    const { key } = event;\n    const activeElement = document.activeElement;\n\n    if (key === 'Escape') {\n      closeAllDropdowns();\n      // Focus the first nav toggle if we were in a dropdown\n      if (activeElement.closest('.nav-dropdown')) {\n        const firstToggle = document.querySelector('.nav-toggle');\n        if (firstToggle) firstToggle.focus();\n      }\n      return;\n    }\n\n    // Arrow key navigation within dropdowns\n    if (activeElement.closest('.nav-dropdown')) {\n      const dropdown = activeElement.closest('.nav-dropdown');\n      const focusableElements = dropdown.querySelectorAll('.nav-dropdown-link, .nav-toggle-level2');\n      const currentIndex = Array.from(focusableElements).indexOf(activeElement);\n\n      if (key === 'ArrowDown') {\n        event.preventDefault();\n        const nextIndex = (currentIndex + 1) % focusableElements.length;\n        focusableElements[nextIndex].focus();\n      } else if (key === 'ArrowUp') {\n        event.preventDefault();\n        const prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;\n        focusableElements[prevIndex].focus();\n      }\n    }\n  }\n\n  // Add event listeners\n  navItems.forEach(navItem => {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.addEventListener('click', handleToggleClick);\n    }\n  });\n\n  // Close dropdowns when clicking outside\n  document.addEventListener('click', (event) => {\n    if (!event.target.closest('.nav-item')) {\n      closeAllDropdowns();\n    }\n  });\n\n  // Handle keyboard navigation\n  document.addEventListener('keydown', handleKeyDown);\n\n  // Handle focus loss (for accessibility)\n  document.addEventListener('focusout', () => {\n    // Small delay to check if focus moved to another nav element\n    setTimeout(() => {\n      const activeElement = document.activeElement;\n      if (!activeElement.closest('.nav-collection')) {\n        closeAllDropdowns();\n      }\n    }, 100);\n  });\n}\n\n// Mobile Navigation Handler\nexport function initMobileNavigation() {\n  const mobileToggle = document.querySelector('.nav-mobile-toggle');\n  const nav = document.querySelector('nav');\n  const navbarContainer = document.querySelector('.navbar-container');\n  const mobileContent = document.querySelector('.nav-mobile-content');\n\n  if (!mobileToggle || !nav || !navbarContainer || !mobileContent) return;\n\n  // Toggle mobile menu\n  function toggleMobileMenu() {\n    const isExpanded = mobileToggle.getAttribute('aria-expanded') === 'true';\n\n    if (isExpanded) {\n      closeMobileMenu();\n    } else {\n      openMobileMenu();\n    }\n  }\n\n  // Open mobile menu (expand navbar)\n  function openMobileMenu() {\n    nav.classList.add('nav-mobile-expanded');\n    navbarContainer.classList.add('nav-expanded');\n    mobileToggle.setAttribute('aria-expanded', 'true');\n    mobileToggle.setAttribute('aria-label', 'Stäng meny');\n\n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n\n    // Reset animations for menu items\n    resetMobileAnimations();\n\n    // Don't auto-focus - let users tab to first item naturally\n    // This prevents the visual focus highlight from appearing immediately\n  }\n\n  // Close mobile menu (collapse navbar)\n  function closeMobileMenu() {\n    nav.classList.remove('nav-mobile-expanded');\n    navbarContainer.classList.remove('nav-expanded');\n    mobileToggle.setAttribute('aria-expanded', 'false');\n    mobileToggle.setAttribute('aria-label', 'Öppna meny');\n\n    // Restore body scroll\n    document.body.style.overflow = '';\n\n    // Close all submenus\n    const mobileItems = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-subitem');\n    mobileItems.forEach(item => {\n      const toggle = item.querySelector('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n      const submenu = item.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      if (toggle && submenu) {\n        toggle.setAttribute('aria-expanded', 'false');\n        submenu.classList.remove('nav-mobile-submenu-active');\n      }\n    });\n\n    // Return focus to toggle button\n    mobileToggle.focus();\n  }\n\n  // Reset mobile menu animations\n  function resetMobileAnimations() {\n    const items = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-cta');\n    items.forEach(item => {\n      // Reset to initial state\n      item.style.opacity = '0';\n      item.style.transform = 'translateY(10px)';\n      item.style.animation = 'none';\n      item.offsetHeight; // Trigger reflow\n      item.style.animation = null;\n      item.style.opacity = null;\n      item.style.transform = null;\n    });\n  }\n\n\n\n  // Handle mobile menu link clicks\n  function handleMobileLinkClick() {\n    // Close mobile menu when a link is clicked\n    closeMobileMenu();\n  }\n\n  // Event listeners\n  mobileToggle.addEventListener('click', toggleMobileMenu);\n\n  // Add submenu toggle listeners (using event delegation for dynamic content)\n  mobileContent.addEventListener('click', (event) => {\n    const toggleButton = event.target.closest('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n    if (toggleButton) {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const parentItem = toggleButton.closest('.nav-mobile-item, .nav-mobile-subitem');\n      const submenu = parentItem?.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';\n\n      if (submenu) {\n        if (isExpanded) {\n          toggleButton.setAttribute('aria-expanded', 'false');\n          submenu.classList.remove('nav-mobile-submenu-active');\n        } else {\n          toggleButton.setAttribute('aria-expanded', 'true');\n          submenu.classList.add('nav-mobile-submenu-active');\n        }\n      }\n    }\n  });\n\n  // Add link click listeners to close menu\n  const mobileLinks = document.querySelectorAll('.nav-mobile-link, .nav-mobile-cta-button');\n  mobileLinks.forEach(link => {\n    link.addEventListener('click', handleMobileLinkClick);\n  });\n\n  // Close menu when clicking outside content area\n  document.addEventListener('click', (event) => {\n    if (nav.classList.contains('nav-mobile-expanded') &&\n        !nav.contains(event.target) &&\n        !mobileToggle.contains(event.target)) {\n      closeMobileMenu();\n    }\n  });\n\n  // Handle escape key\n  document.addEventListener('keydown', (event) => {\n    if (event.key === 'Escape' && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n\n  // Handle window resize to close mobile menu on desktop\n  window.addEventListener('resize', () => {\n    if (window.innerWidth > 1024 && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n}", "/**\n * Main Site JavaScript\n * Minimal setup for OK Tyr website\n */\n\nimport { initScrollNavigation, initDropdownNavigation, initMobileNavigation } from './components/nav.js';\n\n// Simple DOM ready function\nfunction ready(callback) {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    callback();\n  }\n}\n\n// Initialize when DOM is ready\nready(() => {\n  console.log('OK Tyr website loaded');\n\n  // Initialize scroll navigation\n  initScrollNavigation();\n\n  // Initialize dropdown navigation\n  initDropdownNavigation();\n\n  // Initialize mobile navigation\n  initMobileNavigation();\n});\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t752: 0,\n\t146: 0,\n\t938: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunk\"] = self[\"webpackChunk\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\n__webpack_require__.O(undefined, [146,938], () => (__webpack_require__(747)))\n__webpack_require__.O(undefined, [146,938], () => (__webpack_require__(896)))\nvar __webpack_exports__ = __webpack_require__.O(undefined, [146,938], () => (__webpack_require__(698)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["deferred", "initMobileNavigation", "mobileToggle", "document", "querySelector", "nav", "navbarContainer", "mobileContent", "addEventListener", "getAttribute", "closeMobileMenu", "classList", "add", "setAttribute", "body", "style", "overflow", "querySelectorAll", "for<PERSON>ach", "item", "opacity", "transform", "animation", "offsetHeight", "event", "to<PERSON><PERSON><PERSON><PERSON>", "target", "closest", "preventDefault", "stopPropagation", "parentItem", "submenu", "isExpanded", "remove", "link", "handleMobileLinkClick", "contains", "key", "window", "innerWidth", "toggle", "focus", "callback", "console", "log", "navbar", "func", "limit", "inThrottle", "lastScrollY", "scrollY", "throttledScrollHandler", "currentScrollY", "Math", "abs", "args", "arguments", "context", "this", "apply", "setTimeout", "passive", "initScrollNavigation", "navItems", "handleToggleClick", "currentTarget", "navItem", "closeDropdown", "openDropdown", "childItem", "closeAllDropdowns", "length", "activeElement", "focusableElements", "currentIndex", "Array", "from", "indexOf", "firstToggle", "initDropdownNavigation", "readyState", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "m", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "Object", "keys", "every", "splice", "r", "o", "obj", "prop", "prototype", "hasOwnProperty", "call", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "bind", "push", "__webpack_exports__"], "sourceRoot": ""}