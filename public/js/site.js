/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./resources/js/components/nav.js":
/*!****************************************!*\
  !*** ./resources/js/components/nav.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "initDropdownNavigation": () => (/* binding */ initDropdownNavigation),
/* harmony export */   "initMobileNavigation": () => (/* binding */ initMobileNavigation),
/* harmony export */   "initScrollNavigation": () => (/* binding */ initScrollNavigation)
/* harmony export */ });
/**
 * Navigation Component
 * Handles scroll-based navigation behavior and dropdown interactions for OK Tyr website
 */

// Scroll Navigation Handler
function initScrollNavigation() {
  var navbar = document.querySelector('.navbar-container');
  if (!navbar) return;
  var lastScrollY = window.scrollY;

  // Throttle scroll events for better performance
  function throttle(func, limit) {
    var inThrottle;
    return function () {
      var args = arguments;
      var context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(function () {
          return inThrottle = false;
        }, limit);
      }
    };
  }
  function handleScroll() {
    var currentScrollY = window.scrollY;
    var scrollDifference = Math.abs(currentScrollY - lastScrollY);

    // Only react to significant scroll movements (avoid jitter)
    if (scrollDifference < 5) return;
    if (currentScrollY <= 100) {
      // Always show nav when near top of page
      navbar.classList.remove('nav-hidden');
      navbar.classList.add('nav-visible');
    } else if (currentScrollY > lastScrollY && currentScrollY > 200) {
      // Scrolling down - hide nav
      navbar.classList.add('nav-hidden');
      navbar.classList.remove('nav-visible');
    } else if (currentScrollY < lastScrollY) {
      // Scrolling up - show nav
      navbar.classList.remove('nav-hidden');
      navbar.classList.add('nav-visible');
    }
    lastScrollY = currentScrollY;
  }

  // Use throttled scroll handler
  var throttledScrollHandler = throttle(handleScroll, 16); // ~60fps

  // Add scroll listener
  window.addEventListener('scroll', throttledScrollHandler, {
    passive: true
  });

  // Initialize nav state
  navbar.classList.add('nav-visible');
}

// Dropdown Navigation Handler
function initDropdownNavigation() {
  var navItems = document.querySelectorAll('.nav-item');
  if (!navItems.length) return;

  // Handle click events for dropdown toggles
  function handleToggleClick(event) {
    event.preventDefault();
    event.stopPropagation();
    var toggle = event.currentTarget;
    var navItem = toggle.closest('.nav-item, .nav-dropdown-item');
    var isExpanded = toggle.getAttribute('aria-expanded') === 'true';

    // Close all other dropdowns at the same level
    var parentLevel = navItem.closest('.nav-dropdown') ? 'level2' : 'level1';
    if (parentLevel === 'level1') {
      // Close all level 1 dropdowns
      document.querySelectorAll('.nav-item').forEach(function (item) {
        if (item !== navItem) {
          closeDropdown(item);
        }
      });
    } else {
      // Close all level 2 dropdowns in the same parent
      var parentDropdown = navItem.closest('.nav-dropdown');
      parentDropdown.querySelectorAll('.nav-dropdown-item').forEach(function (item) {
        if (item !== navItem) {
          closeDropdown(item);
        }
      });
    }

    // Toggle current dropdown
    if (isExpanded) {
      closeDropdown(navItem);
    } else {
      openDropdown(navItem);
    }
  }

  // Open dropdown
  function openDropdown(navItem) {
    var toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.setAttribute('aria-expanded', 'true');
      navItem.classList.add('nav-active');
    }
  }

  // Close dropdown
  function closeDropdown(navItem) {
    var toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.setAttribute('aria-expanded', 'false');
      navItem.classList.remove('nav-active');

      // Also close any child dropdowns
      navItem.querySelectorAll('.nav-dropdown-item').forEach(function (childItem) {
        closeDropdown(childItem);
      });
    }
  }

  // Close all dropdowns
  function closeAllDropdowns() {
    navItems.forEach(function (navItem) {
      closeDropdown(navItem);
    });
  }

  // Handle keyboard navigation
  function handleKeyDown(event) {
    var key = event.key;
    var activeElement = document.activeElement;
    if (key === 'Escape') {
      closeAllDropdowns();
      // Focus the first nav toggle if we were in a dropdown
      if (activeElement.closest('.nav-dropdown')) {
        var firstToggle = document.querySelector('.nav-toggle');
        if (firstToggle) firstToggle.focus();
      }
      return;
    }

    // Arrow key navigation within dropdowns
    if (activeElement.closest('.nav-dropdown')) {
      var dropdown = activeElement.closest('.nav-dropdown');
      var focusableElements = dropdown.querySelectorAll('.nav-dropdown-link, .nav-toggle-level2');
      var currentIndex = Array.from(focusableElements).indexOf(activeElement);
      if (key === 'ArrowDown') {
        event.preventDefault();
        var nextIndex = (currentIndex + 1) % focusableElements.length;
        focusableElements[nextIndex].focus();
      } else if (key === 'ArrowUp') {
        event.preventDefault();
        var prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
        focusableElements[prevIndex].focus();
      }
    }
  }

  // Add event listeners
  navItems.forEach(function (navItem) {
    var toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.addEventListener('click', handleToggleClick);
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', function (event) {
    if (!event.target.closest('.nav-item')) {
      closeAllDropdowns();
    }
  });

  // Handle keyboard navigation
  document.addEventListener('keydown', handleKeyDown);

  // Handle focus loss (for accessibility)
  document.addEventListener('focusout', function () {
    // Small delay to check if focus moved to another nav element
    setTimeout(function () {
      var activeElement = document.activeElement;
      if (!activeElement.closest('.nav-collection')) {
        closeAllDropdowns();
      }
    }, 100);
  });
}

// Mobile Navigation Handler
function initMobileNavigation() {
  var mobileToggle = document.querySelector('.nav-mobile-toggle');
  var nav = document.querySelector('.navbar');
  var navbarContainer = document.querySelector('.navbar-container');
  var mobileContent = document.querySelector('.nav-mobile-content');
  if (!mobileToggle || !nav || !navbarContainer || !mobileContent) return;

  // Toggle mobile menu
  function toggleMobileMenu() {
    var isExpanded = mobileToggle.getAttribute('aria-expanded') === 'true';
    if (isExpanded) {
      closeMobileMenu();
    } else {
      openMobileMenu();
    }
  }

  // Open mobile menu (expand navbar)
  function openMobileMenu() {
    nav.classList.add('nav-mobile-expanded');
    navbarContainer.classList.add('nav-expanded');
    mobileToggle.setAttribute('aria-expanded', 'true');
    mobileToggle.setAttribute('aria-label', 'Stäng meny');

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Reset animations for menu items
    resetMobileAnimations();

    // Don't auto-focus - let users tab to first item naturally
    // This prevents the visual focus highlight from appearing immediately
  }

  // Close mobile menu (collapse navbar)
  function closeMobileMenu() {
    nav.classList.remove('nav-mobile-expanded');
    navbarContainer.classList.remove('nav-expanded');
    mobileToggle.setAttribute('aria-expanded', 'false');
    mobileToggle.setAttribute('aria-label', 'Öppna meny');

    // Restore body scroll
    document.body.style.overflow = '';

    // Close all submenus
    var mobileItems = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-subitem');
    mobileItems.forEach(function (item) {
      var toggle = item.querySelector('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');
      var submenu = item.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');
      if (toggle && submenu) {
        toggle.setAttribute('aria-expanded', 'false');
        submenu.classList.remove('nav-mobile-submenu-active');
      }
    });

    // Return focus to toggle button
    mobileToggle.focus();
  }

  // Reset mobile menu animations
  function resetMobileAnimations() {
    var items = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-cta');
    items.forEach(function (item) {
      // Reset to initial state
      item.style.opacity = '0';
      item.style.transform = 'translateY(10px)';
      item.style.animation = 'none';
      item.offsetHeight; // Trigger reflow
      item.style.animation = null;
      item.style.opacity = null;
      item.style.transform = null;
    });
  }

  // Handle mobile menu link clicks
  function handleMobileLinkClick() {
    // Close mobile menu when a link is clicked
    closeMobileMenu();
  }

  // Event listeners
  mobileToggle.addEventListener('click', toggleMobileMenu);

  // Add submenu toggle listeners (using event delegation for dynamic content)
  mobileContent.addEventListener('click', function (event) {
    var toggleButton = event.target.closest('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');
    if (toggleButton) {
      event.preventDefault();
      event.stopPropagation();
      var parentItem = toggleButton.closest('.nav-mobile-item, .nav-mobile-subitem');
      var submenu = parentItem === null || parentItem === void 0 ? void 0 : parentItem.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');
      var isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';
      if (submenu) {
        if (isExpanded) {
          toggleButton.setAttribute('aria-expanded', 'false');
          submenu.classList.remove('nav-mobile-submenu-active');
        } else {
          toggleButton.setAttribute('aria-expanded', 'true');
          submenu.classList.add('nav-mobile-submenu-active');
        }
      }
    }
  });

  // Add link click listeners to close menu
  var mobileLinks = document.querySelectorAll('.nav-mobile-link, .nav-mobile-cta-button');
  mobileLinks.forEach(function (link) {
    link.addEventListener('click', handleMobileLinkClick);
  });

  // Close menu when clicking outside content area
  document.addEventListener('click', function (event) {
    if (nav.classList.contains('nav-mobile-expanded') && !nav.contains(event.target) && !mobileToggle.contains(event.target)) {
      closeMobileMenu();
    }
  });

  // Handle escape key
  document.addEventListener('keydown', function (event) {
    if (event.key === 'Escape' && nav.classList.contains('nav-mobile-expanded')) {
      closeMobileMenu();
    }
  });

  // Handle window resize to close mobile menu on desktop
  window.addEventListener('resize', function () {
    if (window.innerWidth > 1024 && nav.classList.contains('nav-mobile-expanded')) {
      closeMobileMenu();
    }
  });
}

/***/ }),

/***/ "./resources/js/site.js":
/*!******************************!*\
  !*** ./resources/js/site.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _components_nav_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/nav.js */ "./resources/js/components/nav.js");
/**
 * Main Site JavaScript
 * Minimal setup for OK Tyr website
 */



// Simple DOM ready function
function ready(callback) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', callback);
  } else {
    callback();
  }
}

// Initialize when DOM is ready
ready(function () {
  console.log('OK Tyr website loaded');

  // Initialize scroll navigation
  (0,_components_nav_js__WEBPACK_IMPORTED_MODULE_0__.initScrollNavigation)();

  // Initialize dropdown navigation
  (0,_components_nav_js__WEBPACK_IMPORTED_MODULE_0__.initDropdownNavigation)();

  // Initialize mobile navigation
  (0,_components_nav_js__WEBPACK_IMPORTED_MODULE_0__.initMobileNavigation)();
});

/***/ }),

/***/ "./resources/css/style.scss":
/*!**********************************!*\
  !*** ./resources/css/style.scss ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./resources/css/tailwind.css":
/*!************************************!*\
  !*** ./resources/css/tailwind.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"/js/site": 0,
/******/ 			"css/tailwind": 0,
/******/ 			"css/style": 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunk"] = self["webpackChunk"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	__webpack_require__.O(undefined, ["css/tailwind","css/style"], () => (__webpack_require__("./resources/js/site.js")))
/******/ 	__webpack_require__.O(undefined, ["css/tailwind","css/style"], () => (__webpack_require__("./resources/css/style.scss")))
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["css/tailwind","css/style"], () => (__webpack_require__("./resources/css/tailwind.css")))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ })()
;
//# sourceMappingURL=site.js.map